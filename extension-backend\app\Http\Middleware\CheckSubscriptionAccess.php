<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Symfony\Component\HttpFoundation\Response;

class CheckSubscriptionAccess
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return \Symfony\Component\HttpFoundation\Response
     */
    public function handle(Request $request, Closure $next): Response
    {
        $user = Auth::guard('anyone')->user();

        if (!$user) {
            return redirect()->route('login');
        }

        // Check if user has active subscription
        if (!$user->subscription_status === 'active' || !$user->subscription_status === 'pending' || $user->subscription_status === 'expired' || $user->subscription_status === 'cancelled'  || $user->subscription_status === 'suspended') {
            return redirect()->route('payment.index')
                ->with('error', 'Anda harus berlangganan untuk mengakses halaman ini.');
        }

        return $next($request);
    }
}
