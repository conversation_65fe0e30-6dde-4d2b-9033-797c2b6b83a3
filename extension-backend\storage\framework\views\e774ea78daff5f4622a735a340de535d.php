<?php $__env->startSection('title', 'Home'); ?>
<?php $__env->startSection('description', 'Satu Pintu - Login to all your favorite apps in one click'); ?>
<?php $__env->startSection('keywords', 'Satu Pintu, login, favorite apps, one click'); ?>

<?php $__env->startSection('content'); ?>

    <!-- Hero Section -->
    <section class="hero section">

        <div class="container" data-aos="fade-up" data-aos-delay="100">
            <div class="row align-items-center">
                <div class="col-lg-6">
                    <div class="hero-content" data-aos="fade-up" data-aos-delay="200">
                        <div class="company-badge mb-4">
                            <i class="bi bi-key-fill me-2"></i>
                            Satu Kunci Untuk Semua Akses
                        </div>

                        <h1 class="mb-4">
                            Akses Premium <br>
                            <PERSON><PERSON> <br>
                            <span class="accent-text"><PERSON><PERSON></span>
                        </h1>

                        <p class="mb-4 mb-md-5">
                            <PERSON><PERSON><PERSON> kemudahan akses ke berbagai aplikasi premium favoritmu hanya dengan satu kunci.
                            Tidak perlu repot mengingat banyak akun, cukup satu pintu untuk semua kebutuhanmu.
                            Hemat waktu dan maksimalkan produktivitasmu.
                        </p>

                        <div class="hero-buttons">
                            <a href="#about" class="btn btn-primary me-0 me-sm-2 mx-1">Mulai Sekarang</a>
                            <a href="https://www.youtube.com/watch?v=Y7f98aduVJ8"
                                class="btn btn-link mt-2 mt-sm-0 glightbox">
                                <i class="bi bi-play-circle me-1"></i>
                                Putar Video
                            </a>
                        </div>
                    </div>
                </div>

                <div class="col-lg-6">
                    <div class="hero-image" data-aos="zoom-out" data-aos-delay="300">
                        <img src="assets/img/illustration-1.webp" alt="Hero Image" class="img-fluid">

                        <div class="customers-badge">
                            <div class="customer-avatars">
                                <?php $__currentLoopData = $sites; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $site): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <?php if($site->logo_path): ?>
                                        <img src="<?php echo e(asset('storage/' . $site->logo_path)); ?>" alt="<?php echo e($site->name); ?>"
                                            class="avatar" title="<?php echo e($site->name); ?>">
                                    <?php else: ?>
                                        <img src="<?php echo e($site->thumbnail ? asset('storage/' . $site->thumbnail) : 'assets/img/avatar-' . (($index % 5) + 1) . '.webp'); ?>"
                                            alt="<?php echo e($site->name); ?>" class="avatar" title="<?php echo e($site->name); ?>">
                                    <?php endif; ?>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                <?php if($sites->count() >= 5): ?>
                                    <span class="avatar more"><?php echo e($sites->count()); ?>+</span>
                                <?php endif; ?>
                            </div>
                            <p class="mb-0 mt-2"><?php echo e($sites->count()); ?>+ aplikasi tersedia untuk memudahkan pekerjaan Anda
                            </p>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row stats-row gy-4 mt-5" data-aos="fade-up" data-aos-delay="500">
                <div class="col-lg-3 col-md-6">
                    <div class="stat-item">
                        <div class="stat-icon">
                            <i class="bi bi-lightning-charge"></i>
                        </div>
                        <div class="stat-content">
                            <h4>Akses Instan</h4>
                            <p class="mb-0">Login otomatis ke semua platform favorit Anda</p>
                        </div>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6">
                    <div class="stat-item">
                        <div class="stat-icon">
                            <i class="bi bi-shield-check"></i>
                        </div>
                        <div class="stat-content">
                            <h4>100% Aman</h4>
                            <p class="mb-0">Data Anda terenkripsi dan tersimpan dengan aman</p>
                        </div>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6">
                    <div class="stat-item">
                        <div class="stat-icon">
                            <i class="bi bi-clock-history"></i>
                        </div>
                        <div class="stat-content">
                            <h4>Hemat Waktu</h4>
                            <p class="mb-0">Tidak perlu mengingat password lagi</p>
                        </div>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6">
                    <div class="stat-item">
                        <div class="stat-icon">
                            <i class="bi bi-globe"></i>
                        </div>
                        <div class="stat-content">
                            <h4>Multi Platform</h4>
                            <p class="mb-0">Bekerja di semua browser dan perangkat</p>
                        </div>
                    </div>
                </div>
            </div>

        </div>

    </section><!-- /Hero Section -->

    <!-- Features Section -->
    <section id="features" class="features section">

        <!-- Section Title -->
        <div class="container section-title" data-aos="fade-up">
            <h2>Aplikasi Premium Tersedia</h2>
            <p>Pilih paket langganan sesuai kebutuhan Anda dan nikmati akses ke berbagai aplikasi premium</p>
        </div><!-- End Section Title -->

        <div class="container">

            <div class="d-flex justify-content-center">

                <ul class="nav nav-tabs" data-aos="fade-up" data-aos-delay="100">

                    <li class="nav-item">
                        <a class="nav-link active show" data-bs-toggle="tab" data-bs-target="#features-tab-1">
                            <h4>Paket Elite</h4>
                        </a>
                    </li><!-- End tab nav item -->

                    <li class="nav-item">
                        <a class="nav-link" data-bs-toggle="tab" data-bs-target="#features-tab-2">
                            <h4>Paket Premium</h4>
                        </a><!-- End tab nav item -->

                    </li>
                    <li class="nav-item">
                        <a class="nav-link" data-bs-toggle="tab" data-bs-target="#features-tab-3">
                            <h4>Semua Paket</h4>
                        </a>
                    </li><!-- End tab nav item -->

                </ul>

            </div>

            <div class="tab-content" data-aos="fade-up" data-aos-delay="200">

                <div class="tab-pane fade active show" id="features-tab-1">
                    <div class="row">
                        <div class="col-lg-6 order-2 order-lg-1 mt-3 mt-lg-0 d-flex flex-column justify-content-center">
                            <h3>Paket Elite - Aplikasi Dasar</h3>
                            <p class="fst-italic">
                                Akses ke aplikasi-aplikasi populer dengan fitur premium yang paling sering digunakan
                                sehari-hari.
                            </p>
                            <div class="row">
                                <?php $__currentLoopData = $sites->whereIn('visibility', ['elite', 'both'])->take(12); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $site): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <div class="col-md-3 mb-3">
                                        <div class="d-flex flex-column align-items-center text-center">
                                            <div class="app-logo-container-large mb-2">
                                                <?php if($site->logo_path): ?>
                                                    <img src="<?php echo e(asset('storage/' . $site->logo_path)); ?>"
                                                        alt="<?php echo e($site->name); ?>" class="app-logo-large">
                                                <?php else: ?>
                                                    <div class="app-logo-placeholder-large">
                                                        <i class="bi bi-check2-all"></i>
                                                    </div>
                                                <?php endif; ?>
                                            </div>
                                            <span class="app-name-center"><?php echo e($site->name); ?></span>
                                        </div>
                                    </div>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </div>
                        </div>
                        <div class="col-lg-6 order-1 order-lg-2 text-center">
                            <img src="assets/img/features-illustration-1.webp" alt="" class="img-fluid">
                        </div>
                    </div>
                </div><!-- End tab content item -->

                <div class="tab-pane fade" id="features-tab-2">
                    <div class="row">
                        <div class="col-lg-6 order-2 order-lg-1 mt-3 mt-lg-0 d-flex flex-column justify-content-center">
                            <h3>Paket Premium - Akses Lengkap</h3>
                            <p class="fst-italic">
                                Dapatkan akses penuh ke semua aplikasi premium termasuk tools profesional dan aplikasi
                                bisnis.
                            </p>
                            <div class="row">
                                <?php $__currentLoopData = $sites->whereIn('visibility', ['premium', 'both'])->take(16); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $site): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <div class="col-md-3 mb-3">
                                        <div class="d-flex flex-column align-items-center text-center">
                                            <div class="app-logo-container-large mb-2">
                                                <?php if($site->logo_path): ?>
                                                    <img src="<?php echo e(asset('storage/' . $site->logo_path)); ?>"
                                                        alt="<?php echo e($site->name); ?>" class="app-logo-large">
                                                <?php else: ?>
                                                    <div class="app-logo-placeholder-large">
                                                        <i class="bi bi-check2-all"></i>
                                                    </div>
                                                <?php endif; ?>
                                            </div>
                                            <span class="app-name-center"><?php echo e($site->name); ?></span>
                                        </div>
                                    </div>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </div>
                        </div>
                        <div class="col-lg-6 order-1 order-lg-2 text-center">
                            <img src="assets/img/features-illustration-2.webp" alt="" class="img-fluid">
                        </div>
                    </div>
                </div><!-- End tab content item -->

                <div class="tab-pane fade" id="features-tab-3">
                    <div class="row">
                        <div class="col-lg-6 order-2 order-lg-1 mt-3 mt-lg-0 d-flex flex-column justify-content-center">
                            <h3>Semua Aplikasi Premium</h3>
                            <p class="fst-italic">
                                Lihat semua aplikasi yang tersedia di platform Satu Pintu. Pilih paket yang sesuai dengan
                                kebutuhan Anda.
                            </p>
                            <div class="row">
                                <?php $__currentLoopData = $sites->take(20); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $site): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <div class="col-md-3 mb-3">
                                        <div class="d-flex flex-column align-items-center text-center position-relative">
                                            <div class="app-logo-container-large mb-2">
                                                <?php if($site->logo_path): ?>
                                                    <img src="<?php echo e(asset('storage/' . $site->logo_path)); ?>"
                                                        alt="<?php echo e($site->name); ?>" class="app-logo-large">
                                                <?php else: ?>
                                                    <div class="app-logo-placeholder-large">
                                                        <i class="bi bi-check2-all"></i>
                                                    </div>
                                                <?php endif; ?>
                                            </div>
                                            <span class="app-name-center"><?php echo e($site->name); ?></span>
                                            <span
                                                class="badge bg-<?php echo e($site->visibility == 'elite' ? 'primary' : ($site->visibility == 'premium' ? 'success' : 'info')); ?> mt-1 app-badge-center">
                                                <?php echo e(ucfirst($site->visibility)); ?>

                                            </span>
                                        </div>
                                    </div>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </div>
                        </div>
                        <div class="col-lg-6 order-1 order-lg-2 text-center">
                            <img src="assets/img/features-illustration-3.webp" alt="" class="img-fluid">
                        </div>
                    </div>
                </div><!-- End tab content item -->

            </div>

        </div>

    </section><!-- /Features Section -->

    <!-- Action Section -->
    <section id="call-to-action" class="call-to-action section">

        <div class="container" data-aos="fade-up" data-aos-delay="100">

            <div class="row content justify-content-center align-items-center position-relative">
                <div class="col-lg-8 mx-auto text-center">
                    <h2 class="display-4 mb-4">Siap Mengakses Ratusan Aplikasi Premium?</h2>
                    <p class="mb-4">Bergabunglah dengan ribuan pengguna yang telah merasakan kemudahan akses ke ChatGPT
                        Pro, Netflix Premium, Spotify Premium, Adobe Creative Suite, dan ratusan aplikasi lainnya hanya
                        dengan satu langganan. Jangan sia-siakan waktu dan uang untuk membeli akun satu per satu!</p>
                    <a href="#" class="btn btn-cta">Mulai Berlangganan Sekarang</a>
                </div>

                <!-- Abstract Background Elements -->
                <div class="shape shape-1">
                    <svg viewBox="0 0 200 200" xmlns="http://www.w3.org/2000/svg">
                        <path
                            d="M47.1,-57.1C59.9,-45.6,68.5,-28.9,71.4,-10.9C74.2,7.1,71.3,26.3,61.5,41.1C51.7,55.9,35,66.2,16.9,69.2C-1.3,72.2,-21,67.8,-36.9,57.9C-52.8,48,-64.9,32.6,-69.1,15.1C-73.3,-2.4,-69.5,-22,-59.4,-37.1C-49.3,-52.2,-32.8,-62.9,-15.7,-64.9C1.5,-67,34.3,-68.5,47.1,-57.1Z"
                            transform="translate(100 100)"></path>
                    </svg>
                </div>

                <div class="shape shape-2">
                    <svg viewBox="0 0 200 200" xmlns="http://www.w3.org/2000/svg">
                        <path
                            d="M41.3,-49.1C54.4,-39.3,66.6,-27.2,71.1,-12.1C75.6,3,72.4,20.9,63.3,34.4C54.2,47.9,39.2,56.9,23.2,62.3C7.1,67.7,-10,69.4,-24.8,64.1C-39.7,58.8,-52.3,46.5,-60.1,31.5C-67.9,16.4,-70.9,-1.4,-66.3,-16.6C-61.8,-31.8,-49.7,-44.3,-36.3,-54C-22.9,-63.7,-8.2,-70.6,3.6,-75.1C15.4,-79.6,28.2,-58.9,41.3,-49.1Z"
                            transform="translate(100 100)"></path>
                    </svg>
                </div>

                <!-- Dot Pattern Groups -->
                <div class="dots dots-1">
                    <svg viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg">
                        <pattern id="dot-pattern" x="0" y="0" width="20" height="20"
                            patternUnits="userSpaceOnUse">
                            <circle cx="2" cy="2" r="2" fill="currentColor"></circle>
                        </pattern>
                        <rect width="100" height="100" fill="url(#dot-pattern)"></rect>
                    </svg>
                </div>

                <div class="dots dots-2">
                    <svg viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg">
                        <pattern id="dot-pattern-2" x="0" y="0" width="20" height="20"
                            patternUnits="userSpaceOnUse">
                            <circle cx="2" cy="2" r="2" fill="currentColor"></circle>
                        </pattern>
                        <rect width="100" height="100" fill="url(#dot-pattern-2)"></rect>
                    </svg>
                </div>

                <div class="shape shape-3">
                    <svg viewBox="0 0 200 200" xmlns="http://www.w3.org/2000/svg">
                        <path
                            d="M43.3,-57.1C57.4,-46.5,71.1,-32.6,75.3,-16.2C79.5,0.2,74.2,19.1,65.1,35.3C56,51.5,43.1,65,27.4,71.7C11.7,78.4,-6.8,78.3,-23.9,72.4C-41,66.5,-56.7,54.8,-65.4,39.2C-74.1,23.6,-75.8,4,-71.7,-13.2C-67.6,-30.4,-57.7,-45.2,-44.3,-56.1C-30.9,-67,-15.5,-74,0.7,-74.9C16.8,-75.8,33.7,-70.7,43.3,-57.1Z"
                            transform="translate(100 100)"></path>
                    </svg>
                </div>
            </div>

        </div>

    </section><!-- /Call To Action Section -->

    <!-- Pricing Section -->
    <section id="pricing" class="pricing section light-background">

        <!-- Section Title -->
        <div class="container section-title" data-aos="fade-up">
            <h2>Paket Berlangganan</h2>
            <p>Pilih paket yang sesuai dengan kebutuhan Anda untuk mengakses ratusan aplikasi premium</p>
        </div><!-- End Section Title -->

        <div class="container" data-aos="fade-up" data-aos-delay="100">

            <div class="row g-4 justify-content-center">

                <?php $__currentLoopData = $plans; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $plan): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <div class="col-lg-5" data-aos="fade-up" data-aos-delay="<?php echo e(100 + $index * 100); ?>">
                        <div class="pricing-card <?php echo e($index == 1 ? 'popular' : ''); ?>">
                            <?php if($index == 1): ?>
                                <div class="popular-badge">Paling Populer</div>
                            <?php endif; ?>
                            <h3><?php echo e($plan->name); ?></h3>
                            <div class="price">
                                <span class="currency">Rp</span>
                                <span class="amount"><?php echo e(number_format($plan->price, 0, ',', '.')); ?></span>
                                <span class="period">/ <?php echo e($plan->duration_text); ?></span>
                            </div>
                            <p class="description"><?php echo e($plan->description); ?></p>

                            <?php if($plan->features && count($plan->features) > 0): ?>
                                <h4>Fitur yang Termasuk:</h4>
                                <ul class="features-list">
                                    <?php $__currentLoopData = $plan->features; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $feature): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <li>
                                            <i class="bi bi-check-circle-fill"></i>
                                            <?php echo e($feature); ?>

                                        </li>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </ul>
                            <?php endif; ?>

                            <a href="<?php echo e(route('payment.index')); ?>"
                                class="btn <?php echo e($index == 1 ? 'btn-light' : 'btn-primary'); ?>">
                                Berlangganan Sekarang
                                <i class="bi bi-arrow-right"></i>
                            </a>
                        </div>
                    </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>

            </div>

        </div>

    </section><!-- /Pricing Section -->

    <!-- Faq Section -->
    <section class="faq-9 faq section light-background" id="faq">

        <div class="container">
            <div class="row">

                <div class="col-lg-5" data-aos="fade-up">
                    <h2 class="faq-title">Ada Pertanyaan? Lihat FAQ</h2>
                    <p class="faq-description">Temukan jawaban atas pertanyaan yang sering diajukan seputar ekstensi Satu
                        Pintu dan cara mengakses puluhan aplikasi premium tanpa perlu login.</p>
                    <div class="faq-arrow d-none d-lg-block" data-aos="fade-up" data-aos-delay="200">
                        <svg class="faq-arrow" width="200" height="211" viewBox="0 0 200 211" fill="none"
                            xmlns="http://www.w3.org/2000/svg">
                            <path
                                d="M198.804 194.488C189.279 189.596 179.529 185.52 169.407 182.07L169.384 182.049C169.227 181.994 169.07 181.939 168.912 181.884C166.669 181.139 165.906 184.546 167.669 185.615C174.053 189.473 182.761 191.837 189.146 195.695C156.603 195.912 119.781 196.591 91.266 179.049C62.5221 161.368 48.1094 130.695 56.934 98.891C84.5539 98.7247 112.556 84.0176 129.508 62.667C136.396 53.9724 146.193 35.1448 129.773 30.2717C114.292 25.6624 93.7109 41.8875 83.1971 51.3147C70.1109 63.039 59.63 78.433 54.2039 95.0087C52.1221 94.9842 50.0776 94.8683 48.0703 94.6608C30.1803 92.8027 11.2197 83.6338 5.44902 65.1074C-1.88449 41.5699 14.4994 19.0183 27.9202 1.56641C28.6411 0.625793 27.2862 -0.561638 26.5419 0.358501C13.4588 16.4098 -0.221091 34.5242 0.896608 56.5659C1.8218 74.6941 14.221 87.9401 30.4121 94.2058C37.7076 97.0203 45.3454 98.5003 53.0334 98.8449C47.8679 117.532 49.2961 137.487 60.7729 155.283C87.7615 197.081 139.616 201.147 184.786 201.155L174.332 206.827C172.119 208.033 174.345 211.287 176.537 210.105C182.06 207.125 187.582 204.122 193.084 201.144C193.346 201.147 195.161 199.887 195.423 199.868C197.08 198.548 193.084 201.144 195.528 199.81C196.688 199.192 197.846 198.552 199.006 197.935C200.397 197.167 200.007 195.087 198.804 194.488ZM60.8213 88.0427C67.6894 72.648 78.8538 59.1566 92.1207 49.0388C98.8475 43.9065 106.334 39.2953 114.188 36.1439C117.295 34.8947 120.798 33.6609 124.168 33.635C134.365 33.5511 136.354 42.9911 132.638 51.031C120.47 77.4222 86.8639 93.9837 58.0983 94.9666C58.8971 92.6666 59.783 90.3603 60.8213 88.0427Z"
                                fill="currentColor"></path>
                        </svg>
                    </div>
                </div>

                <div class="col-lg-7" data-aos="fade-up" data-aos-delay="300">
                    <div class="faq-container">

                        <?php $__empty_1 = true; $__currentLoopData = $faqs; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $faq): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                            <div class="faq-item <?php echo e($index == 0 ? 'faq-active' : ''); ?>">
                                <h3><?php echo e($faq->question); ?></h3>
                                <div class="faq-content">
                                    <p><?php echo e($faq->answer); ?></p>
                                </div>
                                <i class="faq-toggle bi bi-chevron-right"></i>
                            </div><!-- End Faq item-->
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                            <div class="faq-item faq-active">
                                <h3>FAQ sedang dalam proses pembaruan</h3>
                                <div class="faq-content">
                                    <p>Mohon maaf, FAQ sedang dalam proses pembaruan. Silakan hubungi customer service kami
                                        untuk pertanyaan lebih lanjut.</p>
                                </div>
                                <i class="faq-toggle bi bi-chevron-right"></i>
                            </div>
                        <?php endif; ?>

                    </div>
                </div>

            </div>
        </div>
    </section><!-- /Faq Section -->
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layout.homeLayout', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\Belajar Javascript\extension-browser\extension-backend\resources\views/home/<USER>/ ?>