<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Site;
use App\Models\SubscriptionPlan;

class HomeController extends Controller
{
    public function index()
    {
        $sites = Site::where('is_active', true)
            ->orderBy('created_at', 'desc')
            ->limit(5)
            ->get();

        // Ambil subscription plans yang aktif
        $plans = SubscriptionPlan::where('is_active', true)
            ->orderBy('price', 'asc')
            ->get();

        return view('home.index', compact('sites', 'plans'));
    }
}
