<?php

namespace App\Http\Controllers;

use App\Models\Site;
use App\Models\SubscriptionPlan;
use App\Models\Faq;

class HomeController extends Controller
{
    public function index()
    {
        $sites = Site::where('is_active', true)
            ->orderBy('created_at', 'desc')
            ->limit(5)
            ->get();

        // Ambil subscription plans yang aktif
        $plans = SubscriptionPlan::where('is_active', true)
            ->orderBy('price', 'asc')
            ->get();

        // Ambil FAQ yang aktif
        try {
            $faqs = Faq::active()->ordered()->get();
        } catch (\Exception $e) {
            // Fallback jika tabel FAQ belum ada
            $faqs = collect();
        }

        return view('home.index', compact('sites', 'plans', 'faqs'));
    }
}
