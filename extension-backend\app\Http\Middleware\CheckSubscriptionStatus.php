<?php

namespace App\Http\Middleware;

use App\Models\Anyone;
use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class CheckSubscriptionStatus
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Skip check for login and logout routes
        if (in_array($request->route()->getName(), ['api.auth.login', 'api.auth.logout'])) {
            return $next($request);
        }

        $user = $request->user();

        // If user is authenticated, check subscription status
        if ($user->isSubscriptionExpired()) {

            Anyone::where('id', $user->id)->update([
                'subscription_status' => 'expired',
                'payment_status' => 'pending',
                'subscription_status' => 'expired',
                'subscription_plan' => null,
                'role' => 'none',
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Akun belum diaktivasi. Masa aktif akun Anda telah berakhir.',
                'subscription_expired' => true,
            ], 403);
        }

        return $next($request);
    }
}
