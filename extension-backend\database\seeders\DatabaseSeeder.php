<?php

namespace Database\Seeders;

use App\Models\User;
use App\Models\Anyone;
use App\Models\Site;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        // Seed categories first
        $this->call(CategorySeeder::class);

        // Seed important info
        $this->call(ImportantInfoSeeder::class);

        // Seed manual payments
        $this->call(ManualPaymentSeeder::class);

        // Seed subscription plans
        $this->call(SubscriptionPlanSeeder::class);

        // Create admin user (already exists)
        User::factory()->create([
            'name' => 'Administrator',
            'email' => '<EMAIL>',
            'password' => Hash::make('12345678'),
        ]);

        // Create elite users
        Anyone::create([
            'name' => 'Elite User 1',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'role' => 'elite',
            'is_active' => true,
        ]);

        Anyone::create([
            'name' => 'Elite User 2',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'role' => 'elite',
            'is_active' => true,
        ]);

        // Create premium users
        Anyone::create([
            'name' => 'Premium User 1',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'role' => 'premium',
            'is_active' => true,
        ]);

        Anyone::create([
            'name' => 'Premium User 2',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'role' => 'premium',
            'is_active' => true,
        ]);

        // Create sites with functional categories
        Site::create([
            'name' => 'Facebook',
            'url' => 'https://facebook.com',
            'domain' => 'facebook.com',
            'category' => 'social-media',
            'visibility' => 'elite',
            'description' => 'Social media platform',
            'is_active' => true,
        ]);

        Site::create([
            'name' => 'Twitter',
            'url' => 'https://twitter.com',
            'domain' => 'twitter.com',
            'category' => 'social-media',
            'visibility' => 'both',
            'description' => 'Microblogging platform',
            'is_active' => true,
        ]);

        Site::create([
            'name' => 'Netflix',
            'url' => 'https://netflix.com',
            'domain' => 'netflix.com',
            'category' => 'streaming',
            'visibility' => 'both',
            'description' => 'Streaming platform',
            'is_active' => true,
        ]);

        Site::create([
            'name' => 'Amazon Prime',
            'url' => 'https://primevideo.com',
            'domain' => 'primevideo.com',
            'category' => 'streaming',
            'visibility' => 'both',
            'description' => 'Prime video streaming',
            'is_active' => true,
        ]);

        Site::create([
            'name' => 'YouTube Premium',
            'url' => 'https://youtube.com',
            'domain' => 'youtube.com',
            'category' => 'streaming',
            'visibility' => 'premium',
            'description' => 'Video sharing platform with premium features',
            'is_active' => true,
        ]);

        Site::create([
            'name' => 'ChatGPT',
            'url' => 'https://chatgpt.com',
            'domain' => 'chatgpt.com',
            'category' => 'artificial-intelligence',
            'visibility' => 'both',
            'description' => 'AI-powered conversational assistant',
            'cookie_file_path' => 'cookie-files/01JZHK8HN8D0JFH8CKPNYSJ421.json',
            'cookie_file_uploaded_at' => now(),
            'is_active' => true,
        ]);

        // Additional sites with functional categories
        Site::create([
            'name' => 'Instagram',
            'url' => 'https://instagram.com',
            'domain' => 'instagram.com',
            'category' => 'social-media',
            'visibility' => 'both',
            'description' => 'Photo and video sharing social platform',
            'is_active' => true,
        ]);

        Site::create([
            'name' => 'LinkedIn',
            'url' => 'https://linkedin.com',
            'domain' => 'linkedin.com',
            'category' => 'social-media',
            'visibility' => 'elite',
            'description' => 'Professional networking platform',
            'is_active' => true,
        ]);

        Site::create([
            'name' => 'TikTok',
            'url' => 'https://tiktok.com',
            'domain' => 'tiktok.com',
            'category' => 'social-media',
            'visibility' => 'both',
            'description' => 'Short-form video sharing platform',
            'is_active' => true,
        ]);

        Site::create([
            'name' => 'Discord',
            'url' => 'https://discord.com',
            'domain' => 'discord.com',
            'category' => 'social-media',
            'visibility' => 'both',
            'description' => 'Voice, video and text communication platform',
            'is_active' => true,
        ]);

        Site::create([
            'name' => 'GitHub',
            'url' => 'https://github.com',
            'domain' => 'github.com',
            'category' => 'development',
            'visibility' => 'elite',
            'description' => 'Code hosting and collaboration platform',
            'is_active' => true,
        ]);

        Site::create([
            'name' => 'Reddit',
            'url' => 'https://reddit.com',
            'domain' => 'reddit.com',
            'category' => 'social-media',
            'visibility' => 'both',
            'description' => 'Social news aggregation and discussion platform',
            'is_active' => true,
        ]);

        // More sites with functional categories
        Site::create([
            'name' => 'Spotify',
            'url' => 'https://spotify.com',
            'domain' => 'spotify.com',
            'category' => 'streaming',
            'visibility' => 'both',
            'description' => 'Music streaming platform',
            'is_active' => true,
        ]);

        Site::create([
            'name' => 'Disney+',
            'url' => 'https://disneyplus.com',
            'domain' => 'disneyplus.com',
            'category' => 'streaming',
            'visibility' => 'premium',
            'description' => 'Disney streaming service',
            'is_active' => true,
        ]);

        Site::create([
            'name' => 'Hulu',
            'url' => 'https://hulu.com',
            'domain' => 'hulu.com',
            'category' => 'streaming',
            'visibility' => 'both',
            'description' => 'Video streaming platform',
            'is_active' => true,
        ]);

        Site::create([
            'name' => 'HBO Max',
            'url' => 'https://hbomax.com',
            'domain' => 'hbomax.com',
            'category' => 'streaming',
            'visibility' => 'premium',
            'description' => 'Premium streaming service',
            'is_active' => true,
        ]);

        Site::create([
            'name' => 'Apple Music',
            'url' => 'https://music.apple.com',
            'domain' => 'music.apple.com',
            'category' => 'streaming',
            'visibility' => 'both',
            'description' => 'Apple music streaming service',
            'is_active' => true,
        ]);

        Site::create([
            'name' => 'Canva Pro',
            'url' => 'https://canva.com',
            'domain' => 'canva.com',
            'category' => 'design',
            'visibility' => 'both',
            'description' => 'Graphic design platform with premium features',
            'is_active' => true,
        ]);

        Site::create([
            'name' => 'Adobe Creative Cloud',
            'url' => 'https://adobe.com',
            'domain' => 'adobe.com',
            'category' => 'design',
            'visibility' => 'premium',
            'description' => 'Creative software suite',
            'is_active' => true,
        ]);

        Site::create([
            'name' => 'Figma',
            'url' => 'https://figma.com',
            'domain' => 'figma.com',
            'category' => 'design',
            'visibility' => 'both',
            'description' => 'Collaborative design tool',
            'is_active' => true,
        ]);

        Site::create([
            'name' => 'Notion',
            'url' => 'https://notion.so',
            'domain' => 'notion.so',
            'category' => 'productivity',
            'visibility' => 'both',
            'description' => 'All-in-one workspace for notes and collaboration',
            'is_active' => true,
        ]);
    }
}
