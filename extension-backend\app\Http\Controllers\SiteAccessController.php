<?php

namespace App\Http\Controllers;

use App\Models\Site;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class SiteAccessController extends Controller
{
    public function index()
    {
        /** @var \App\Models\Anyone $user */
        $user = Auth::guard('anyone')->user();
        
        if (!$user) {
            return redirect()->route('login');
        }

        // Filter sites based on user's role and subscription plan
        $sites = Site::where('is_active', true)
            ->where(function ($query) use ($user) {
                // Check visibility based on user's role
                switch ($user->role) {
                    case 'elite':
                        $query->whereIn('visibility', ['elite', 'both']);
                        break;
                    case 'premium':
                        $query->whereIn('visibility', ['premium', 'both']);
                        break;
                    default:
                        $query->where('visibility', 'both');
                }
            })
            ->with(['jsonFiles'])
            ->orderBy('name')
            ->get();

        return view('sites.index', [
            'user' => $user,
            'sites' => $sites,
        ]);
    }

    public function show(Site $site)
    {
        /** @var \App\Models\Anyone $user */
        $user = Auth::guard('anyone')->user();
        
        if (!$user) {
            return redirect()->route('login');
        }

        // Check if user has access to this site
        $hasAccess = false;
        switch ($user->role) {
            case 'elite':
                $hasAccess = in_array($site->visibility, ['elite', 'both']);
                break;
            case 'premium':
                $hasAccess = in_array($site->visibility, ['premium', 'both']);
                break;
            default:
                $hasAccess = $site->visibility === 'both';
        }

        if (!$hasAccess) {
            abort(403, 'Anda tidak memiliki akses ke situs ini');
        }

        return view('sites.show', [
            'user' => $user,
            'site' => $site,
        ]);
    }
}