<?php $__env->startSection('title', '<PERSON><PERSON> - <PERSON>'); ?>

<?php $__env->startPush('styles'); ?>
    <script src="https://cdn.jsdelivr.net/npm/@tailwindcss/browser@4"></script>
<?php $__env->stopPush(); ?>

<?php $__env->startSection('content'); ?>
<div class="min-h-screen bg-gray-50 py-12 my-30">
    <div class="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Header -->
        <div class="text-center mb-8">
            <h1 class="text-3xl font-bold text-gray-900 mb-2"><PERSON><PERSON></h1>
            <p class="text-gray-600">Pilih paket yang sesuai untuk perpanjangan akun <PERSON></p>
        </div>

        <!-- Warning Alert -->
        <?php if($showWarning): ?>
        <div class="bg-yellow-50 border-l-4 border-yellow-400 p-4 mb-6">
            <div class="flex">
                <div class="flex-shrink-0">
                    <svg class="h-5 w-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
                    </svg>
                </div>
                <div class="ml-3">
                    <p class="text-sm text-yellow-700">
                        <strong>Peringatan!</strong> Akun Anda akan berakhir dalam <?php echo e($daysLeft); ?> hari. Silakan lakukan pembayaran untuk perpanjangan.
                    </p>
                </div>
            </div>
        </div>
        <?php endif; ?>



        <!-- Subscription Plans -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-8 mb-8">
            <?php $__currentLoopData = $plans; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $plan): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <div class="bg-white rounded-lg shadow-lg overflow-hidden border border-gray-200">
                <div class="bg-gradient-to-r from-blue-500 to-blue-600 px-6 py-4">
                    <h3 class="text-xl font-bold text-white"><?php echo e($plan->name); ?></h3>
                    <p class="text-blue-100"><?php echo e($plan->description); ?></p>
                </div>
                <div class="p-6">
                    <div class="text-center mb-6">
                        <div class="text-4xl font-bold text-gray-900">
                            <?php echo e($plan->formatted_price); ?>

                        </div>
                        <div class="text-gray-600">/<?php echo e($plan->duration_text); ?></div>
                    </div>

                    <ul class="space-y-3 mb-6">
                        <?php $__currentLoopData = $plan->features ?? []; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $feature): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <li class="flex items-start">
                            <svg class="h-5 w-5 text-green-500 mr-2 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
                            </svg>
                            <span class="text-gray-700"><?php echo e($feature); ?></span>
                        </li>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </ul>

                    <button onclick="selectPlan(<?php echo e($plan->id); ?>, '<?php echo e($plan->name); ?>', <?php echo e($plan->price); ?>, '<?php echo e($plan->duration_text); ?>', '<?php echo e($plan->whatsapp_number ?? '*************'); ?>')"
                        class="w-full bg-blue-600 hover:bg-blue-700 text-white font-medium py-3 px-4 rounded-lg transition duration-200">
                        Pilih <?php echo e($plan->name); ?>

                    </button>
                </div>
            </div>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </div>

        <!-- Payment Instructions -->
        <div class="mt-8 bg-blue-50 rounded-lg p-6">
            <h3 class="text-lg font-medium text-blue-900 mb-4">Petunjuk Pembayaran</h3>
            <div class="text-sm text-blue-800">
                <p class="mb-2"><strong>1.</strong> Pilih paket langganan yang sesuai dengan kebutuhan Anda.</p>
                <p class="mb-2"><strong>2.</strong> Klik tombol "Pilih Paket" untuk mengarahkan ke WhatsApp admin.</p>
                <p class="mb-2"><strong>3.</strong> Kirim pesan otomatis yang sudah disediakan.</p>
                <p class="mt-4"><strong>Catatan:</strong> Admin akan memverifikasi pembayaran Anda dalam waktu 1x24 jam.</p>
            </div>
        </div>
    </div>
</div>

<!-- SweetAlert2 Script -->
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
<script>
    <?php if(session('success')): ?>
    Swal.fire({
        icon: 'success',
        title: 'Berhasil!',
        text: '<?php echo e(session("success")); ?>',
        confirmButtonText: 'OK'
    });
    <?php endif; ?>

    <?php if(session('error')): ?>
    Swal.fire({
        icon: 'error',
        title: 'Oops...',
        text: '<?php echo e(session("error")); ?>',
        confirmButtonText: 'OK'
    });
    <?php endif; ?>

    // WhatsApp functions
    function contactAdmin() {
                const message = `Halo admin, saya ingin konfirmasi pembayaran untuk akun <?php echo e($user->email); ?>`;
                const whatsappUrl = `https://wa.me/*************?text=${encodeURIComponent(message)}`;
                window.open(whatsappUrl, '_blank');
            }

            function showPaymentGuide() {
                Swal.fire({
                    title: 'Panduan Pembayaran',
                    html: `
                        <div class="text-left space-y-4">
                            <div class="bg-blue-50 p-4 rounded-lg">
                                <h4 class="font-semibold text-blue-800 mb-2">📱 Transfer Bank</h4>
                                <p class="text-blue-700 text-sm">Transfer ke rekening admin sesuai dengan jumlah yang ditentukan. Simpan bukti transfer sebagai bukti pembayaran.</p>
                            </div>
                            <div class="bg-green-50 p-4 rounded-lg">
                                <h4 class="font-semibold text-green-800 mb-2">💵 Tunai</h4>
                                <p class="text-green-700 text-sm">Lakukan pembayaran langsung ke lokasi yang ditentukan oleh admin. Minta tanda terima sebagai bukti.</p>
                            </div>
                            <div class="bg-purple-50 p-4 rounded-lg">
                                <h4 class="font-semibold text-purple-800 mb-2">📲 E-Wallet</h4>
                                <p class="text-purple-700 text-sm">Transfer melalui aplikasi e-wallet favorit Anda. Screenshot bukti transfer sebagai bukti pembayaran.</p>
                            </div>
                            <div class="bg-yellow-50 p-4 rounded-lg">
                                <p class="text-yellow-800 text-sm"><strong>Catatan:</strong> Setelah pembayaran dikirim, admin akan memverifikasi dalam waktu 1x24 jam.</p>
                            </div>
                        </div>
                    `,
                    icon: 'info',
                    confirmButtonText: 'Mengerti',
                    confirmButtonColor: '#3B82F6',
                    width: 600
                });
            }

    // Form submission handler with SweetAlert2 confirmation
    document.querySelector('form').addEventListener('submit', function(e) {
        e.preventDefault();

        const amount = document.getElementById('amount').value;
        const paymentMethod = document.getElementById('payment_method').value;
        const paymentProof = document.getElementById('payment_proof').files[0];

        if (!amount || !paymentMethod || !paymentProof) {
            Swal.fire({
                icon: 'warning',
                title: 'Perhatian',
                text: 'Harap lengkapi semua field yang diperlukan!',
                confirmButtonText: 'OK'
            });
            return;
        }

        // Show confirmation dialog
        Swal.fire({
            title: 'Konfirmasi Pembayaran',
            html: `
                <div class="text-left">
                    <p><strong>Jumlah:</strong> Rp ${parseInt(amount).toLocaleString('id-ID')}</p>
                    <p><strong>Metode:</strong> ${paymentMethod.replace('_', ' ').toUpperCase()}</p>
                    <p class="text-sm text-gray-600 mt-2">Pastikan data yang Anda masukkan sudah benar.</p>
                </div>
            `,
            icon: 'question',
            showCancelButton: true,
            confirmButtonText: 'Ya, Kirim',
            cancelButtonText: 'Batal',
            confirmButtonColor: '#3B82F6',
            cancelButtonColor: '#EF4444'
        }).then((result) => {
            if (result.isConfirmed) {
                // Show loading
                Swal.fire({
                    title: 'Mengirim...',
                    text: 'Mohon tunggu, sedang memproses pembayaran Anda',
                    allowOutsideClick: false,
                    showConfirmButton: false,
                    willOpen: () => {
                        Swal.showLoading();
                    }
                });

                // Submit form
                this.submit();
            }
        });
    });

    // File upload preview
    document.getElementById('payment_proof').addEventListener('change', function(e) {
        const file = e.target.files[0];
        if (file) {
            if (file.size > 2 * 1024 * 1024) {
                Swal.fire({
                    icon: 'error',
                    title: 'File Terlalu Besar',
                    text: 'Ukuran file maksimal 2MB',
                    confirmButtonText: 'OK'
                });
                this.value = '';
                return;
            }

            if (!['image/jpeg', 'image/png', 'image/jpg'].includes(file.type)) {
                Swal.fire({
                    icon: 'error',
                    title: 'Format Tidak Valid',
                    text: 'Hanya menerima file JPG, PNG, atau JPEG',
                    confirmButtonText: 'OK'
                });
                this.value = '';
                return;
            }
        }
    });
</script>

<!-- WhatsApp Template Modal -->
<div id="whatsappModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden z-50">
    <div class="flex items-center justify-center min-h-screen p-4">
        <div class="bg-white rounded-lg max-w-md w-full p-6">
            <h3 class="text-lg font-semibold mb-4">Konfirmasi Pembelian</h3>
            <p class="text-gray-600 mb-4">
                Anda akan diarahkan ke WhatsApp admin untuk melakukan pembayaran paket <span id="selectedPlanName" class="font-semibold"></span>.
            </p>
            <div class="flex justify-end space-x-3">
                <button onclick="closeModal()" class="px-4 py-2 text-gray-600 border border-gray-300 rounded-md hover:bg-gray-50">
                    Batal
                </button>
                <button id="confirmWhatsApp" class="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700">
                    Lanjut ke WhatsApp
                </button>
            </div>
        </div>
    </div>
</div>

<script>
function selectPlan(planId, planName, price, duration, whatsappNumber) {
    document.getElementById('selectedPlanName').textContent = planName;
    document.getElementById('whatsappModal').classList.remove('hidden');

    const message = `Halo Admin Satu Pintu,\n\nSaya ingin membeli paket ${planName} dengan harga Rp ${price.toLocaleString('id-ID')} untuk ${duration}.\n\nMohon informasi pembayarannya.\n\nTerima kasih.`;

    const whatsappUrl = `https://wa.me/${whatsappNumber}?text=${encodeURIComponent(message)}`;

    document.getElementById('confirmWhatsApp').onclick = function() {
        window.open(whatsappUrl, '_blank');
        closeModal();
    };
}

function closeModal() {
    document.getElementById('whatsappModal').classList.add('hidden');
}

// Close modal when clicking outside
window.onclick = function(event) {
    const modal = document.getElementById('whatsappModal');
    if (event.target === modal) {
        closeModal();
    }
}
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layout.homeLayout', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\Belajar Javascript\extension-browser\extension-backend\resources\views/payment/index.blade.php ENDPATH**/ ?>